# 🚪 JWT Bước 5: Logo<PERSON> và Token Blacklist Đơn Giản

## 📚 Mục tiêu của bài này
- Hiểu vấn đề với JWT khi logout
- Tạo Token Blacklist đơn giản
- Implement logout functionality
- <PERSON><PERSON><PERSON> c<PERSON>ch "thu hồi" JWT tokens

---

## 🤔 Bước 1: Vấn Đề Với JWT Logout

### Vấn đề JWT Stateless:

**Ví dụ thực tế:**
- **Thẻ ATM**: <PERSON>ân hàng có thể "khóa" thẻ ngay lập tức
- **Thẻ nhân viên**: <PERSON><PERSON><PERSON> ty có thể "vô hiệu hóa" thẻ khi nhân viên nghỉ việc
- **JWT Token**: <PERSON><PERSON><PERSON><PERSON> thể "thu hồi" token trước khi hết hạn!

### Scenario vấn đề:

```
1. User đăng nhập → Nhận JWT token (30 phút)
2. User logout sau 5 phút
3. Token vẫn hợp lệ trong 25 phút tiếp theo!
4. <PERSON><PERSON>u token bị đánh cắp → Attacker c<PERSON> thể sử dụng 25 phút
```

**Tại sao không thể thu hồi JWT?**
- JWT là **stateless** → Server không lưu trữ token
- Token chứa tất cả thông tin cần thiết
- Server chỉ verify signature, không check database

### Giải pháp Token Blacklist:

**Concept đơn giản:**
- Lưu danh sách tokens bị "cấm" (blacklist)
- Khi verify token → Kiểm tra blacklist trước
- Nếu token trong blacklist → Từ chối request

**Ví dụ:**
```
Blacklist = ["token123", "token456", "token789"]

Khi user gửi "token123" → Check blacklist → Tìm thấy → Từ chối
Khi user gửi "token999" → Check blacklist → Không tìm thấy → Cho phép
```

---

## 📝 Bước 2: Tạo Token Blacklist Đơn Giản

### 2.1 Tạo file `jwt_with_blacklist.py`

```python
# jwt_with_blacklist.py
from fastapi import FastAPI, Request, HTTPException, status
from fastapi.responses import JSONResponse
from jose import jwt, JWTError
from datetime import datetime, timedelta
from typing import Set
import time

app = FastAPI(title="JWT với Blacklist Đơn Giản")

# JWT config
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

# In-memory blacklist (thực tế sẽ dùng Redis hoặc Database)
TOKEN_BLACKLIST: Set[str] = set()

print("🚪 JWT với Blacklist API khởi động!")
print(f"📋 Blacklist hiện tại: {len(TOKEN_BLACKLIST)} tokens")
```

**Giải thích:**
- `Set[str]`: Python set để lưu blacklisted tokens
- In-memory: Lưu trong RAM (production sẽ dùng Redis)
- Set có performance tốt cho việc check membership

### 2.2 Tạo blacklist functions

```python
def add_token_to_blacklist(token: str):
    """
    Thêm token vào blacklist
    
    Args:
        token: JWT token string cần blacklist
    """
    TOKEN_BLACKLIST.add(token)
    print(f"🚫 Token đã được thêm vào blacklist: {token[:20]}...")
    print(f"📊 Tổng tokens trong blacklist: {len(TOKEN_BLACKLIST)}")

def is_token_blacklisted(token: str) -> bool:
    """
    Kiểm tra token có trong blacklist không
    
    Args:
        token: JWT token string
    
    Returns:
        bool: True nếu token bị blacklist
    """
    is_blacklisted = token in TOKEN_BLACKLIST
    if is_blacklisted:
        print(f"🚫 Token bị blacklist: {token[:20]}...")
    return is_blacklisted

def get_blacklist_stats():
    """
    Lấy thống kê blacklist
    
    Returns:
        dict: Thông tin về blacklist
    """
    return {
        "total_blacklisted_tokens": len(TOKEN_BLACKLIST),
        "blacklist_type": "in-memory",
        "note": "Production nên dùng Redis"
    }
```

**Giải thích:**
- `add_token_to_blacklist()`: Thêm token vào blacklist
- `is_token_blacklisted()`: Kiểm tra token có bị blacklist không
- `get_blacklist_stats()`: Thống kê để debug

### 2.3 Cập nhật middleware với blacklist check

```python
@app.middleware("http")
async def jwt_middleware_with_blacklist(request: Request, call_next):
    """
    Middleware kiểm tra JWT token và blacklist
    """
    start_time = time.time()
    
    print(f"🌐 Request: {request.method} {request.url.path}")
    
    # Public routes
    public_paths = ["/", "/login", "/refresh", "/docs", "/openapi.json"]
    
    if request.url.path in public_paths:
        print(f"✅ Public route: {request.url.path}")
        response = await call_next(request)
        return response
    
    # Lấy Authorization header
    authorization = request.headers.get("Authorization")
    
    if not authorization:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Thiếu Authorization header"}
        )
    
    # Parse Bearer token
    try:
        scheme, token = authorization.split(" ")
        if scheme.lower() != "bearer":
            raise ValueError("Invalid scheme")
    except ValueError:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Header phải có format: Bearer <token>"}
        )
    
    # KIỂM TRA BLACKLIST TRƯỚC KHI VERIFY TOKEN
    if is_token_blacklisted(token):
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "detail": "Token đã bị thu hồi (logged out)",
                "error_code": "TOKEN_BLACKLISTED"
            }
        )
```

**Giải thích:**
- Kiểm tra blacklist TRƯỚC khi verify token
- Tiết kiệm CPU: Không cần decode token đã bị blacklist
- Error code cụ thể để client hiểu lý do

```python
    # Verify JWT token
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra token type (nếu có)
        token_type = payload.get("type", "access")
        if token_type != "access":
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Phải sử dụng Access Token"}
            )
        
        print(f"✅ Token hợp lệ và không bị blacklist: {payload.get('username')}")
        request.state.user = payload
        request.state.current_token = token  # Lưu token để logout
        
    except jwt.ExpiredSignatureError:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Token đã hết hạn"}
        )
    except JWTError:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Token không hợp lệ"}
        )
    
    # Gọi API handler
    response = await call_next(request)
    
    # Thêm process time
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    return response
```

**Giải thích:**
- `request.state.current_token = token`: Lưu token để dùng cho logout
- Verify token như bình thường sau khi check blacklist

---

## 🚪 Bước 3: Implement Logout

### 3.1 Fake users và login API

```python
# Fake users
FAKE_USERS = {
    "admin": "password123",
    "user1": "mypassword"
}

@app.get("/")
def root():
    return {
        "message": "🌟 JWT API với Logout!",
        "endpoints": {
            "login": "POST /login",
            "logout": "POST /logout",
            "protected": "GET /protected",
            "blacklist-stats": "GET /blacklist-stats"
        }
    }

@app.post("/login")
def login(username: str, password: str):
    """
    API đăng nhập
    """
    if username not in FAKE_USERS or FAKE_USERS[username] != password:
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    # Tạo Access Token
    access_expire = datetime.utcnow() + timedelta(minutes=30)
    access_token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "type": "access",
        "exp": access_expire
    }
    access_token = jwt.encode(access_token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    # Tạo Refresh Token
    refresh_expire = datetime.utcnow() + timedelta(days=7)
    refresh_token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "type": "refresh",
        "exp": refresh_expire
    }
    refresh_token = jwt.encode(refresh_token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    print(f"🔐 User {username} đăng nhập thành công")
    
    return {
        "message": f"Đăng nhập thành công! Chào {username}",
        "access_token": access_token,
        "refresh_token": refresh_token,
        "access_expires_in": 1800,  # 30 phút
        "refresh_expires_in": 604800  # 7 ngày
    }
```

### 3.2 Logout API

```python
@app.post("/logout")
def logout(request: Request):
    """
    API logout - thêm current token vào blacklist
    
    Args:
        request: FastAPI request (chứa user info và token từ middleware)
    
    Returns:
        dict: Logout confirmation
    """
    # Lấy thông tin user từ middleware
    user = request.state.user
    current_token = request.state.current_token
    
    username = user.get("username")
    
    print(f"🚪 User {username} đang logout")
    
    # Thêm current access token vào blacklist
    add_token_to_blacklist(current_token)
    
    return {
        "message": f"Logout thành công! Tạm biệt {username}",
        "note": "Access token đã được thu hồi",
        "blacklisted_token": current_token[:20] + "...",
        "blacklist_stats": get_blacklist_stats()
    }
```

**Giải thích:**
- Lấy user info và token từ middleware
- Thêm current token vào blacklist
- Token không thể sử dụng được nữa

### 3.3 Logout với Refresh Token

```python
@app.post("/logout-all")
def logout_all(request: Request, refresh_token: str):
    """
    API logout tất cả sessions - blacklist cả access và refresh token
    
    Args:
        request: FastAPI request
        refresh_token: Refresh token để blacklist
    
    Returns:
        dict: Logout confirmation
    """
    user = request.state.user
    current_access_token = request.state.current_token
    
    username = user.get("username")
    
    print(f"🚪 User {username} đang logout tất cả sessions")
    
    # Verify refresh token trước khi blacklist
    try:
        refresh_payload = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
        
        if refresh_payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phải cung cấp Refresh Token"
            )
        
        if refresh_payload.get("username") != username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Refresh Token không thuộc về user này"
            )
        
        # Blacklist cả 2 tokens
        add_token_to_blacklist(current_access_token)
        add_token_to_blacklist(refresh_token)
        
        return {
            "message": f"Logout tất cả sessions thành công! Tạm biệt {username}",
            "blacklisted_tokens": 2,
            "note": "Cả Access Token và Refresh Token đã bị thu hồi",
            "blacklist_stats": get_blacklist_stats()
        }
        
    except jwt.ExpiredSignatureError:
        # Refresh token hết hạn, chỉ blacklist access token
        add_token_to_blacklist(current_access_token)
        
        return {
            "message": f"Logout thành công! Refresh Token đã hết hạn",
            "blacklisted_tokens": 1,
            "blacklist_stats": get_blacklist_stats()
        }
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Refresh Token không hợp lệ"
        )
```

---

## 🛡️ Bước 4: Protected APIs và Admin Functions

### 4.1 Protected APIs

```python
@app.get("/protected")
def protected_route(request: Request):
    """
    API được bảo vệ - middleware đã check token và blacklist
    """
    user = request.state.user
    
    return {
        "message": "🎉 Truy cập thành công!",
        "user_info": {
            "user_id": user.get("user_id"),
            "username": user.get("username")
        },
        "protected_data": "Dữ liệu được bảo vệ",
        "note": "Token đã được verify và không bị blacklist"
    }

@app.get("/me")
def get_my_info(request: Request):
    """
    API lấy thông tin user hiện tại
    """
    user = request.state.user
    
    return {
        "message": "Thông tin user hiện tại",
        "user_details": user,
        "token_status": "active",
        "blacklist_check": "passed"
    }
```

### 4.2 Admin functions

```python
@app.get("/blacklist-stats")
def blacklist_stats():
    """
    API lấy thống kê blacklist (public để dễ test)
    """
    stats = get_blacklist_stats()
    
    # Thêm thông tin chi tiết
    stats["sample_tokens"] = [
        token[:20] + "..." for token in list(TOKEN_BLACKLIST)[:3]
    ]
    
    return {
        "message": "Thống kê Token Blacklist",
        "stats": stats,
        "note": "Trong production, API này nên được bảo vệ"
    }

@app.post("/admin/clear-blacklist")
def clear_blacklist(admin_password: str):
    """
    API xóa tất cả tokens khỏi blacklist (chỉ để test)
    """
    if admin_password != "admin123":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Sai admin password"
        )
    
    global TOKEN_BLACKLIST
    old_count = len(TOKEN_BLACKLIST)
    TOKEN_BLACKLIST.clear()
    
    return {
        "message": "Đã xóa tất cả tokens khỏi blacklist",
        "cleared_tokens": old_count,
        "current_blacklist_size": len(TOKEN_BLACKLIST)
    }
```

---

## 🧪 Bước 5: Test Logout Flow

### 5.1 Chạy server

```bash
uvicorn jwt_with_blacklist:app --reload --port 8000
```

### 5.2 Test complete logout flow

**1. Login:**
```bash
curl "http://localhost:8000/login?username=admin&password=password123"
```

**Response:**
```json
{
  "message": "Đăng nhập thành công! Chào admin",
  "access_token": "eyJhbGci...",
  "refresh_token": "eyJhbGci...",
  "access_expires_in": 1800,
  "refresh_expires_in": 604800
}
```

**2. Sử dụng access token:**
```bash
curl -H "Authorization: Bearer ACCESS_TOKEN" \
  http://localhost:8000/protected
```
**Kết quả:** ✅ Success

**3. Logout:**
```bash
curl -X POST \
  -H "Authorization: Bearer ACCESS_TOKEN" \
  http://localhost:8000/logout
```

**4. Thử sử dụng token sau logout:**
```bash
curl -H "Authorization: Bearer ACCESS_TOKEN" \
  http://localhost:8000/protected
```
**Kết quả:** ❌ 401 - "Token đã bị thu hồi (logged out)"

### 5.3 Test blacklist stats

**Check blacklist:**
```bash
curl http://localhost:8000/blacklist-stats
```

**Clear blacklist (để test lại):**
```bash
curl -X POST \
  "http://localhost:8000/admin/clear-blacklist?admin_password=admin123"
```

### 5.4 Test logout-all

**1. Login và lấy cả 2 tokens**
**2. Logout all sessions:**
```bash
curl -X POST \
  -H "Authorization: Bearer ACCESS_TOKEN" \
  "http://localhost:8000/logout-all?refresh_token=REFRESH_TOKEN"
```

**3. Thử refresh token:**
```bash
curl "http://localhost:8000/refresh?refresh_token=REFRESH_TOKEN"
```
**Kết quả:** ❌ 401 - Token bị blacklist

---

## 🎯 Tóm tắt Bài 5

### Những gì đã học:

1. **JWT Logout Problem**: JWT stateless không thể thu hồi
2. **Token Blacklist**: Giải pháp để "thu hồi" tokens
3. **Logout Implementation**: Thêm token vào blacklist
4. **Middleware Integration**: Check blacklist trước khi verify

### Ưu điểm Token Blacklist:

- ✅ **Immediate Revocation**: Thu hồi token ngay lập tức
- ✅ **Security**: Ngăn chặn sử dụng token sau logout
- ✅ **Flexible**: Có thể blacklist từng token riêng lẻ
- ✅ **Admin Control**: Admin có thể thu hồi token users

### Nhược điểm và cải thiện:

- ❌ **Memory Usage**: In-memory blacklist tốn RAM
- ✅ **Solution**: Dùng Redis với TTL
- ❌ **Performance**: Check blacklist mỗi request
- ✅ **Solution**: Redis rất nhanh, có thể cache

### Production Considerations:

- 🔄 **Redis**: Thay in-memory bằng Redis
- ⏰ **TTL**: Token tự động xóa khỏi blacklist khi hết hạn
- 📊 **Monitoring**: Track blacklist size và performance
- 🔄 **Cleanup**: Định kỳ xóa expired tokens

### Bài tập về nhà:

1. Test logout flow với nhiều users
2. Thử blacklist refresh tokens
3. Monitor blacklist size

---

**🎉 Hoàn thành! Bạn đã xây dựng JWT system hoàn chỉnh!**

### 🏆 Tổng kết 5 bài học:

1. ✅ **Bài 1**: Hiểu JWT cơ bản - Tạo và verify token
2. ✅ **Bài 2**: Thêm expiration và FastAPI đơn giản
3. ✅ **Bài 3**: Bảo vệ routes với Authorization header
4. ✅ **Bài 4**: Middleware tự động và Refresh Token
5. ✅ **Bài 5**: Logout và Token Blacklist

**Bạn đã có một JWT Authentication System hoàn chỉnh từ cơ bản đến nâng cao!** 🚀
