# 🛡️ JWT Bước 2: Thêm Thời <PERSON>ian <PERSON>ết <PERSON>ạn và FastAPI Đơn Giản

## 📚 Mục tiêu của bài này
- Thêm thời gian hết hạn cho JWT token
- Tạo API đăng nhập đơn giản với FastAPI
- Tạo API được bảo vệ bởi JWT
- Hiểu cách JWT hoạt động trong web

---

## 🔧 Bước 1: Tạo Authentication Middleware

### 1.1 Tạo file `app/middleware/auth_middleware.py`

```python
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable, List
import time
from ..core.jwt_utils import verify_jwt_token

class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware tự động kiểm tra JWT token cho mọi request
    """
    
    def __init__(self, app, exclude_paths: List[str] = None):
        """
        Khởi tạo middleware
        
        Args:
            app: FastAPI application instance
            exclude_paths: Danh sách paths không cần authentication
        """
        super().__init__(app)
        
        # Danh sách paths không cần token (public routes)
        self.exclude_paths = exclude_paths or [
            "/",                    # Root endpoint
            "/docs",               # Swagger UI
            "/redoc",              # ReDoc
            "/openapi.json",       # OpenAPI schema
            "/auth/login",         # Login endpoint
            "/health",             # Health check
            "/favicon.ico"         # Browser favicon request
        ]
        
        print(f"🛡️ AuthenticationMiddleware initialized")
        print(f"📋 Excluded paths: {self.exclude_paths}")
```
**Giải thích:**
- `BaseHTTPMiddleware`: Base class cho custom middleware
- `exclude_paths`: Danh sách routes không cần authentication
- Login endpoint phải được exclude để user có thể đăng nhập
- Docs endpoints được exclude để dễ test API

### 1.2 Implement dispatch method

```python
    async def dispatch(self, request: Request, call_next: Callable):
        """
        Xử lý mỗi request trước khi đến endpoint
        
        Args:
            request: HTTP request object
            call_next: Function để gọi endpoint tiếp theo
        
        Returns:
            Response: HTTP response
        """
        start_time = time.time()
        
        # Log request info
        print(f"🌐 {request.method} {request.url.path} from {self._get_client_ip(request)}")
        
        # Kiểm tra path có cần authentication không
        if self._is_excluded_path(request.url.path):
            print(f"✅ Path {request.url.path} is excluded from authentication")
            response = await call_next(request)
            self._add_process_time_header(response, start_time)
            return response
```
**Giải thích:**
- `dispatch()`: Method chính của middleware, được gọi cho mọi request
- `call_next`: Function để tiếp tục xử lý request
- Log thông tin request để debug và monitor
- Kiểm tra path có trong exclude list không

```python
        # Lấy Authorization header
        authorization = request.headers.get("Authorization")
        
        if not authorization:
            print("❌ Missing Authorization header")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "detail": "Authorization header is required",
                    "error_code": "MISSING_AUTH_HEADER"
                }
            )
```
**Giải thích:**
- `request.headers.get("Authorization")`: Lấy Authorization header
- Nếu không có header → return 401 Unauthorized ngay lập tức
- `JSONResponse`: Trả về JSON response thay vì raise HTTPException

```python
        # Kiểm tra Bearer token format
        try:
            scheme, token = authorization.split(" ", 1)
            if scheme.lower() != "bearer":
                raise ValueError("Invalid authorization scheme")
        except ValueError:
            print(f"❌ Invalid authorization header format: {authorization}")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "detail": "Invalid authorization header format. Expected: Bearer <token>",
                    "error_code": "INVALID_AUTH_FORMAT"
                }
            )
```
**Giải thích:**
- `authorization.split(" ", 1)`: Tách header thành scheme và token
- Expected format: "Bearer <jwt_token>"
- `scheme.lower() != "bearer"`: Kiểm tra scheme phải là "bearer"
- ValueError nếu format không đúng

```python
        # Verify JWT token
        payload = verify_jwt_token(token)
        if payload is None:
            print(f"❌ Invalid or expired token: {token[:20]}...")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "detail": "Invalid or expired token",
                    "error_code": "INVALID_TOKEN"
                }
            )
```
**Giải thích:**
- Gọi `verify_jwt_token()` để kiểm tra token
- Nếu return None → token không hợp lệ hoặc hết hạn
- Log token (chỉ 20 ký tự đầu) để debug
- Return 401 với error code cụ thể

```python
        # Lưu user info vào request state để sử dụng trong endpoints
        user_id = payload.get("sub")
        username = payload.get("username")
        role = payload.get("role")
        
        request.state.current_user = {
            "user_id": int(user_id) if user_id else None,
            "username": username,
            "role": role,
            "token_payload": payload
        }
        
        print(f"✅ User authenticated: {username} (ID: {user_id}, Role: {role})")
```
**Giải thích:**
- `request.state`: Object để lưu data giữa middleware và endpoints
- Lưu thông tin user để endpoints có thể sử dụng
- Convert user_id từ string về int
- Log thông tin user đã authenticate

```python
        # Tiếp tục xử lý request
        try:
            response = await call_next(request)
            self._add_process_time_header(response, start_time)
            return response
        except Exception as e:
            print(f"❌ Error processing request: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "detail": "Internal server error",
                    "error_code": "PROCESSING_ERROR"
                }
            )
```
**Giải thích:**
- `await call_next(request)`: Gọi endpoint handler
- Thêm process time vào response header
- Try-catch để handle lỗi trong quá trình xử lý

### 1.3 Helper methods

```python
    def _is_excluded_path(self, path: str) -> bool:
        """
        Kiểm tra path có được exclude khỏi authentication không
        
        Args:
            path: Request path
        
        Returns:
            bool: True nếu path được exclude
        """
        # Exact match
        if path in self.exclude_paths:
            return True
        
        # Prefix match cho dynamic paths
        for excluded_path in self.exclude_paths:
            if path.startswith(excluded_path):
                return True
        
        return False
```
**Giải thích:**
- Kiểm tra exact match trước (path == excluded_path)
- Sau đó kiểm tra prefix match (path.startswith())
- Prefix match hữu ích cho dynamic paths như `/auth/reset-password/{token}`

```python
    def _get_client_ip(self, request: Request) -> str:
        """
        Lấy IP address của client
        
        Args:
            request: HTTP request
        
        Returns:
            str: Client IP address
        """
        # Kiểm tra X-Forwarded-For header (khi có proxy/load balancer)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Lấy IP đầu tiên (client thực)
            return forwarded_for.split(",")[0].strip()
        
        # Kiểm tra X-Real-IP header
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
```
**Giải thích:**
- `X-Forwarded-For`: Header khi có proxy/load balancer
- `X-Real-IP`: Alternative header cho real IP
- `request.client.host`: Direct connection IP
- Fallback chain để đảm bảo luôn có IP

```python
    def _add_process_time_header(self, response, start_time: float):
        """
        Thêm process time vào response header
        
        Args:
            response: HTTP response
            start_time: Thời gian bắt đầu xử lý request
        """
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = f"{process_time:.4f}"
```
**Giải thích:**
- Tính thời gian xử lý request
- Thêm vào header để monitor performance
- Format 4 chữ số thập phân

---

## 🔧 Bước 2: Role-based Authorization Middleware

### 2.1 Tạo file `app/middleware/role_middleware.py`

```python
from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Dict, List, Callable

class RoleAuthorizationMiddleware(BaseHTTPMiddleware):
    """
    Middleware kiểm tra quyền truy cập dựa trên role
    """
    
    def __init__(self, app, role_permissions: Dict[str, List[str]] = None):
        """
        Args:
            app: FastAPI application
            role_permissions: Mapping role -> list of allowed paths
        """
        super().__init__(app)
        
        # Định nghĩa permissions cho từng role
        self.role_permissions = role_permissions or {
            "admin": ["/admin", "/users", "/system"],      # Admin có thể truy cập tất cả
            "moderator": ["/moderate", "/reports"],        # Moderator có quyền hạn chế
            "user": ["/profile", "/dashboard"]             # User chỉ truy cập basic features
        }
        
        print(f"🔐 RoleAuthorizationMiddleware initialized")
        print(f"📋 Role permissions: {self.role_permissions}")
```
**Giải thích:**
- Định nghĩa permissions cho từng role
- Admin có quyền cao nhất, user có quyền thấp nhất
- Có thể customize role_permissions khi khởi tạo

```python
    async def dispatch(self, request: Request, call_next: Callable):
        """
        Kiểm tra quyền truy cập dựa trên role
        """
        # Chỉ check role nếu user đã được authenticate
        if not hasattr(request.state, 'current_user'):
            # Nếu chưa authenticate, để AuthenticationMiddleware handle
            return await call_next(request)
        
        current_user = request.state.current_user
        user_role = current_user.get("role")
        request_path = request.url.path
        
        print(f"🔍 Checking role permission: {user_role} for path {request_path}")
```
**Giải thích:**
- Chỉ check role nếu user đã được authenticate
- `hasattr(request.state, 'current_user')`: Kiểm tra user info có tồn tại không
- Lấy role và path để kiểm tra permission

```python
        # Kiểm tra user có quyền truy cập path này không
        if not self._has_permission(user_role, request_path):
            print(f"❌ Access denied: {user_role} cannot access {request_path}")
            return JSONResponse(
                status_code=403,
                content={
                    "detail": f"Access denied. Role '{user_role}' cannot access this resource.",
                    "error_code": "INSUFFICIENT_PERMISSIONS",
                    "required_permissions": self._get_required_permissions(request_path)
                }
            )
        
        print(f"✅ Access granted: {user_role} can access {request_path}")
        return await call_next(request)
```
**Giải thích:**
- Gọi `_has_permission()` để kiểm tra quyền
- Return 403 Forbidden nếu không có quyền
- Cung cấp thông tin về required permissions

```python
    def _has_permission(self, role: str, path: str) -> bool:
        """
        Kiểm tra role có quyền truy cập path không
        """
        if not role:
            return False
        
        # Admin có quyền truy cập tất cả
        if role == "admin":
            return True
        
        # Kiểm tra permissions của role
        allowed_paths = self.role_permissions.get(role, [])
        
        for allowed_path in allowed_paths:
            if path.startswith(allowed_path):
                return True
        
        return False
    
    def _get_required_permissions(self, path: str) -> List[str]:
        """
        Lấy danh sách roles có thể truy cập path
        """
        required_roles = []
        for role, paths in self.role_permissions.items():
            if any(path.startswith(allowed_path) for allowed_path in paths):
                required_roles.append(role)
        return required_roles
```

---

## 🚀 Bước 3: Cập nhật Main Application

### 3.1 Cập nhật `app/main.py`

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .middleware.auth_middleware import AuthenticationMiddleware
from .middleware.role_middleware import RoleAuthorizationMiddleware
from .api.auth_basic import router as auth_router

# Tạo FastAPI app
app = FastAPI(
    title="JWT with Middleware",
    description="Authentication và Authorization với Middleware",
    version="1.0.0"
)

# CORS Middleware (thêm trước custom middlewares)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```
**Giải thích:**
- CORS middleware phải được thêm trước custom middlewares
- Allow credentials để gửi Authorization header

```python
# Role Authorization Middleware (thêm trước Authentication)
app.add_middleware(
    RoleAuthorizationMiddleware,
    role_permissions={
        "admin": ["/admin", "/users", "/system", "/auth/admin"],
        "moderator": ["/moderate", "/reports", "/auth/protected"],
        "user": ["/profile", "/dashboard", "/auth/me", "/auth/protected"]
    }
)

# Authentication Middleware
app.add_middleware(
    AuthenticationMiddleware,
    exclude_paths=[
        "/",
        "/docs",
        "/redoc", 
        "/openapi.json",
        "/auth/login",
        "/health",
        "/favicon.ico"
    ]
)
```
**Giải thích:**
- Middleware được thêm theo thứ tự ngược lại với thứ tự thực thi
- Authentication middleware chạy trước (check token)
- Role middleware chạy sau (check permissions)

```python
# Include routers
app.include_router(auth_router)

@app.get("/")
def root():
    return {
        "message": "JWT with Middleware API",
        "features": [
            "Automatic token validation",
            "Role-based authorization", 
            "Request logging",
            "Performance monitoring"
        ]
    }

@app.get("/health")
def health_check():
    return {"status": "healthy", "middleware": "active"}
```

---

## 🧪 Bước 4: Cập nhật Endpoints để sử dụng Middleware

### 4.1 Cập nhật `app/api/auth_basic.py`

```python
# Thêm endpoints sử dụng request.state thay vì dependencies

@router.get("/me-middleware")
def get_current_user_middleware(request: Request):
    """
    Lấy thông tin user từ middleware (không cần dependency)
    
    Args:
        request: FastAPI request object
    
    Returns:
        Dict: Thông tin user từ middleware
    """
    # Lấy user info từ request.state (được set bởi middleware)
    current_user = request.state.current_user
    
    print(f"📋 Getting user info from middleware: {current_user['username']}")
    
    return {
        "message": f"Hello {current_user['username']} (via middleware)!",
        "user_info": current_user,
        "middleware_data": "This data comes from middleware authentication",
        "token_payload": current_user["token_payload"]
    }
```
**Giải thích:**
- Không cần `Depends(get_current_user)` vì middleware đã check token
- `request.state.current_user`: User info được set bởi middleware
- Đơn giản hơn vì không cần dependency injection

```python
@router.get("/admin/users")
def list_users_admin(request: Request):
    """
    Admin endpoint - chỉ admin mới truy cập được (via role middleware)
    """
    current_user = request.state.current_user
    
    # Middleware đã check role = admin, nên ở đây chắc chắn là admin
    return {
        "message": f"Admin {current_user['username']} accessing user list",
        "users": list(FAKE_USERS.keys()),
        "admin_privileges": ["view_all_users", "manage_users", "system_admin"],
        "accessed_via": "role_middleware"
    }

@router.get("/moderate/reports")  
def view_reports_moderator(request: Request):
    """
    Moderator endpoint - admin và moderator mới truy cập được
    """
    current_user = request.state.current_user
    
    return {
        "message": f"Moderator {current_user['username']} viewing reports",
        "reports": ["Report 1", "Report 2", "Report 3"],
        "moderator_actions": ["approve", "reject", "escalate"]
    }
```

---

## 🧪 Bước 5: Testing Middleware

### 5.1 Test scenarios

**1. Test public endpoint (không cần token):**
```bash
curl -X GET "http://localhost:8001/"
```
**Expected:** ✅ Success (middleware bypass)

**2. Test protected endpoint không có token:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware"
```
**Expected:** ❌ 401 Unauthorized

**3. Test với invalid token:**
```bash
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer invalid-token"
```
**Expected:** ❌ 401 Invalid token

**4. Test với valid token:**
```bash
# Trước tiên login để lấy token
curl -X POST "http://localhost:8001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "user1", "password": "user123"}'

# Sau đó dùng token
curl -X GET "http://localhost:8001/auth/me-middleware" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```
**Expected:** ✅ Success với user info

**5. Test role-based access:**
```bash
# User thường truy cập admin endpoint
curl -X GET "http://localhost:8001/admin/users" \
  -H "Authorization: Bearer USER_TOKEN"
```
**Expected:** ❌ 403 Forbidden

```bash
# Admin truy cập admin endpoint  
curl -X GET "http://localhost:8001/admin/users" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```
**Expected:** ✅ Success với admin data

### 5.2 Middleware logs

Khi chạy server, bạn sẽ thấy logs như:

```
🛡️ AuthenticationMiddleware initialized
📋 Excluded paths: ['/', '/docs', '/redoc', '/openapi.json', '/auth/login', '/health', '/favicon.ico']
🔐 RoleAuthorizationMiddleware initialized
📋 Role permissions: {'admin': ['/admin', '/users', '/system'], 'moderator': ['/moderate', '/reports'], 'user': ['/profile', '/dashboard']}

🌐 GET /auth/me-middleware from 127.0.0.1
🔍 Checking token: eyJhbGciOiJIUzI1NiIs...
✅ Token decoded successfully for user: user1
✅ User authenticated: user1 (ID: 2, Role: user)
🔍 Checking role permission: user for path /auth/me-middleware
✅ Access granted: user can access /auth/me-middleware
```

---

## 📝 Tóm tắt

### Middleware Architecture:

```
Request → CORS → Role Authorization → Authentication → Endpoint
                     ↓                      ↓
                Check permissions      Check JWT token
                     ↓                      ↓
                403 if denied         401 if invalid
```

### Ưu điểm của Middleware:

- ✅ **Tự động**: Không cần thêm dependencies vào mỗi endpoint
- ✅ **Centralized**: Logic authentication ở một chỗ
- ✅ **Performance**: Thêm process time monitoring
- ✅ **Logging**: Tự động log mọi request
- ✅ **Flexible**: Dễ dàng exclude/include paths

### Files đã tạo:

- `app/middleware/auth_middleware.py`: JWT authentication middleware
- `app/middleware/role_middleware.py`: Role-based authorization middleware
- Updated `app/main.py`: Middleware configuration
- Updated `app/api/auth_basic.py`: Endpoints sử dụng middleware

### Next steps:

- ✅ JWT cơ bản hoàn thành
- ✅ Middleware Authorization hoàn thành  
- 🔄 Tiếp theo: Refresh Token flow

---

*Middleware giúp code sạch hơn và bảo mật tự động cho toàn bộ application!*
