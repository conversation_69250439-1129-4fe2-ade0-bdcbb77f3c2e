# 🔄 JWT Bước 4: Middleware Đơn Giản và Refresh Token

## 📚 Mục tiêu của bài này
- Hiểu tại sao cần Middleware
- Tạo Middleware đơn giản để tự động check token
- Hiểu vấn đề token hết hạn
- Tạo Refresh Token đơn giản

---

## 🤔 Bước 1: Tại Sao Cần Middleware?

### Vấn đề hiện tại:

**Ở bài trước, mỗi API protected đều phải:**
```python
@app.get("/protected")
def protected_route(authorization: Optional[str] = Header(None)):
    token = get_token_from_header(authorization)  # Lặp lại
    user_info = verify_jwt_token(token)           # Lặp lại
    # ... logic API
```

**Nếu có 10 API protected → Phải copy code 10 lần!**

### Giải pháp Middleware:

**Middleware = "Người gác cổng tự động"**
- Đ<PERSON><PERSON> ở cửa, kiểm tra mọi người vào
- <PERSON>ế<PERSON> có thẻ hợp lệ → Cho vào
- Nếu không có thẻ → Chặn lại

**Với Middleware:**
```python
# Middleware tự động check token cho TẤT CẢ requests
# API chỉ cần viết logic chính

@app.get("/protected")
def protected_route():
    # Không cần check token nữa!
    # Middleware đã check rồi
    return {"message": "Dữ liệu được bảo vệ"}
```

---

## 🛡️ Bước 2: Tạo Middleware Đơn Giản

### 2.1 Hiểu cách Middleware hoạt động

```
Request → Middleware → API Handler → Response
    ↓         ↓            ↓           ↑
  Check     Pass/       Process     Return
  Token     Block       Request     Data
```

### 2.2 Tạo file `simple_middleware.py`

```python
# simple_middleware.py
from fastapi import FastAPI, Request, HTTPException, status
from fastapi.responses import JSONResponse
import time

app = FastAPI(title="JWT với Middleware Đơn Giản")

# JWT config (giống như trước)
from jose import jwt, JWTError
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

print("🛡️ Middleware JWT API khởi động!")
```

### 2.3 Tạo middleware function

```python
@app.middleware("http")
async def jwt_middleware(request: Request, call_next):
    """
    Middleware kiểm tra JWT token cho mọi request
    
    Args:
        request: HTTP request
        call_next: Function để gọi API handler tiếp theo
    
    Returns:
        Response: HTTP response
    """
    start_time = time.time()
    
    print(f"🌐 Request: {request.method} {request.url.path}")
    
    # Danh sách paths không cần check token (public routes)
    public_paths = ["/", "/login", "/docs", "/openapi.json"]
    
    if request.url.path in public_paths:
        print(f"✅ Public route: {request.url.path}")
        response = await call_next(request)
        return response
```

**Giải thích:**
- `@app.middleware("http")`: Đăng ký middleware cho HTTP requests
- `call_next`: Function để gọi API handler tiếp theo
- `public_paths`: Danh sách routes không cần token
- Nếu là public route → Bỏ qua check token

```python
    # Lấy Authorization header
    authorization = request.headers.get("Authorization")
    
    if not authorization:
        print("❌ Không có Authorization header")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Thiếu Authorization header"}
        )
    
    # Parse Bearer token
    try:
        scheme, token = authorization.split(" ")
        if scheme.lower() != "bearer":
            raise ValueError("Invalid scheme")
    except ValueError:
        print("❌ Authorization header sai format")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Header phải có format: Bearer <token>"}
        )
```

**Giải thích:**
- Lấy Authorization header từ request
- Parse để tách "Bearer" và token
- Return JSON error nếu không hợp lệ

```python
    # Verify JWT token
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        print(f"✅ Token hợp lệ cho user: {payload.get('username')}")
        
        # Lưu thông tin user vào request để API sử dụng
        request.state.user = payload
        
    except jwt.ExpiredSignatureError:
        print("❌ Token đã hết hạn")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Token đã hết hạn"}
        )
    except JWTError:
        print("❌ Token không hợp lệ")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Token không hợp lệ"}
        )
    
    # Gọi API handler
    response = await call_next(request)
    
    # Thêm thời gian xử lý vào response header
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    return response
```

**Giải thích:**
- Verify JWT token như bài trước
- `request.state.user = payload`: Lưu thông tin user vào request
- API handler có thể lấy thông tin user từ `request.state.user`
- Thêm process time vào response header

### 2.4 Tạo API handlers đơn giản

```python
# Fake users (giống như trước)
FAKE_USERS = {
    "admin": "password123",
    "user1": "mypassword"
}

@app.get("/")
def root():
    return {
        "message": "🌟 JWT API với Middleware!",
        "note": "Middleware tự động check token cho protected routes"
    }

@app.post("/login")
def login(username: str, password: str):
    """
    API đăng nhập đơn giản (dùng query parameters)
    """
    if username not in FAKE_USERS or FAKE_USERS[username] != password:
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    # Tạo token
    from datetime import datetime, timedelta
    expire_time = datetime.utcnow() + timedelta(minutes=30)
    
    token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "exp": expire_time
    }
    
    token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    return {
        "message": f"Đăng nhập thành công! Chào {username}",
        "token": token,
        "expires_in": 1800
    }
```

### 2.5 Protected APIs (không cần check token nữa!)

```python
@app.get("/protected")
def protected_route(request: Request):
    """
    API được bảo vệ - Middleware đã check token rồi!
    """
    # Lấy thông tin user từ middleware
    user = request.state.user
    
    return {
        "message": "🎉 Truy cập thành công qua Middleware!",
        "user_info": {
            "user_id": user.get("user_id"),
            "username": user.get("username")
        },
        "protected_data": "Dữ liệu được bảo vệ bởi Middleware"
    }

@app.get("/me")
def get_my_info(request: Request):
    """
    API lấy thông tin user - cũng được bảo vệ bởi Middleware
    """
    user = request.state.user
    
    return {
        "message": "Thông tin user từ Middleware",
        "user_details": user,
        "middleware_note": "Token đã được Middleware verify"
    }

@app.get("/admin-only")
def admin_only(request: Request):
    """
    API chỉ dành cho admin
    """
    user = request.state.user
    
    # Check role (đơn giản)
    if user.get("username") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Chỉ admin mới truy cập được"
        )
    
    return {
        "message": "🔑 Chào admin!",
        "admin_data": "Dữ liệu chỉ admin mới thấy được"
    }
```

**Giải thích:**
- API không cần check token nữa!
- Middleware đã check và lưu user info vào `request.state.user`
- API chỉ cần lấy thông tin user và xử lý logic

---

## 🔄 Bước 3: Hiểu Vấn Đề Token Hết Hạn

### Vấn đề User Experience:

**Scenario:**
1. User đăng nhập → Nhận token (30 phút)
2. User làm việc 25 phút → Token còn 5 phút
3. User tiếp tục làm việc → Token hết hạn
4. User phải đăng nhập lại → **UX tệ!**

### Giải pháp Refresh Token:

**Concept:**
- **Access Token**: Ngắn hạn (30 phút), dùng cho API calls
- **Refresh Token**: Dài hạn (7 ngày), dùng để lấy Access Token mới

**Flow:**
```
1. Login → Access Token (30 min) + Refresh Token (7 days)
2. Use Access Token for API calls
3. Access Token expires → Use Refresh Token to get new Access Token
4. Continue with new Access Token
```

---

## 🔄 Bước 4: Implement Refresh Token Đơn Giản

### 4.1 Cập nhật login API

```python
@app.post("/login")
def login(username: str, password: str):
    if username not in FAKE_USERS or FAKE_USERS[username] != password:
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    from datetime import datetime, timedelta
    
    # Tạo Access Token (ngắn hạn)
    access_expire = datetime.utcnow() + timedelta(minutes=5)  # 5 phút để test
    access_token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "type": "access",  # Đánh dấu loại token
        "exp": access_expire
    }
    access_token = jwt.encode(access_token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    # Tạo Refresh Token (dài hạn)
    refresh_expire = datetime.utcnow() + timedelta(days=7)
    refresh_token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "type": "refresh",  # Đánh dấu loại token
        "exp": refresh_expire
    }
    refresh_token = jwt.encode(refresh_token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    return {
        "message": f"Đăng nhập thành công! Chào {username}",
        "access_token": access_token,
        "refresh_token": refresh_token,
        "access_expires_in": 300,  # 5 phút
        "refresh_expires_in": 604800  # 7 ngày
    }
```

**Giải thích:**
- Tạo 2 tokens: access (5 phút) và refresh (7 ngày)
- `type`: Phân biệt loại token
- Access token ngắn để test dễ dàng

### 4.2 Cập nhật middleware check token type

```python
@app.middleware("http")
async def jwt_middleware(request: Request, call_next):
    # ... (code trước giữ nguyên đến phần verify token)
    
    # Verify JWT token
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra loại token
        token_type = payload.get("type")
        if token_type != "access":
            print(f"❌ Token type không đúng: {token_type}")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Phải sử dụng Access Token"}
            )
        
        print(f"✅ Access Token hợp lệ cho user: {payload.get('username')}")
        request.state.user = payload
        
    except jwt.ExpiredSignatureError:
        print("❌ Access Token đã hết hạn")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "detail": "Access Token đã hết hạn",
                "hint": "Sử dụng Refresh Token để lấy Access Token mới"
            }
        )
    # ... (phần còn lại giữ nguyên)
```

### 4.3 Tạo API refresh token

```python
@app.post("/refresh")
def refresh_token(refresh_token: str):
    """
    API để refresh Access Token bằng Refresh Token
    
    Args:
        refresh_token: Refresh Token từ login
    
    Returns:
        dict: Access Token mới
    """
    try:
        # Verify Refresh Token
        payload = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Kiểm tra loại token
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Phải sử dụng Refresh Token"
            )
        
        username = payload.get("username")
        user_id = payload.get("user_id")
        
        print(f"🔄 Refresh token cho user: {username}")
        
        # Tạo Access Token mới
        from datetime import datetime, timedelta
        access_expire = datetime.utcnow() + timedelta(minutes=5)
        new_access_token_data = {
            "user_id": user_id,
            "username": username,
            "type": "access",
            "exp": access_expire
        }
        new_access_token = jwt.encode(new_access_token_data, SECRET_KEY, algorithm=ALGORITHM)
        
        return {
            "message": "Access Token đã được refresh!",
            "access_token": new_access_token,
            "expires_in": 300
        }
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh Token đã hết hạn, vui lòng đăng nhập lại"
        )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh Token không hợp lệ"
        )
```

---

## 🧪 Bước 5: Test Middleware và Refresh Token

### 5.1 Chạy server

```bash
uvicorn simple_middleware:app --reload --port 8000
```

### 5.2 Test Middleware

**1. Test public route:**
```bash
curl http://localhost:8000/
```
**Kết quả:** ✅ Success (không cần token)

**2. Test protected route không có token:**
```bash
curl http://localhost:8000/protected
```
**Kết quả:** ❌ 401 - "Thiếu Authorization header"

**3. Login và test với token:**
```bash
# Login
curl "http://localhost:8000/login?username=admin&password=password123"

# Copy access_token và test
curl -H "Authorization: Bearer ACCESS_TOKEN" \
  http://localhost:8000/protected
```

### 5.3 Test Refresh Token Flow

**1. Login để lấy cả 2 tokens:**
```bash
curl "http://localhost:8000/login?username=admin&password=password123"
```

**2. Sử dụng access token (5 phút):**
```bash
curl -H "Authorization: Bearer ACCESS_TOKEN" \
  http://localhost:8000/me
```

**3. Đợi 6 phút (hoặc thay đổi expire time thành 10 giây để test nhanh):**

**4. Access token hết hạn:**
```bash
curl -H "Authorization: Bearer ACCESS_TOKEN" \
  http://localhost:8000/me
```
**Kết quả:** ❌ 401 - "Access Token đã hết hạn"

**5. Sử dụng refresh token để lấy access token mới:**
```bash
curl "http://localhost:8000/refresh?refresh_token=REFRESH_TOKEN"
```

**6. Sử dụng access token mới:**
```bash
curl -H "Authorization: Bearer NEW_ACCESS_TOKEN" \
  http://localhost:8000/me
```

---

## 🎯 Tóm tắt Bài 4

### Những gì đã học:

1. **Middleware**: Tự động check token cho tất cả requests
2. **Request State**: Lưu user info để API sử dụng
3. **Refresh Token**: Giải quyết vấn đề token hết hạn
4. **Token Types**: Phân biệt access và refresh token

### Ưu điểm của Middleware:

- ✅ **DRY**: Không lặp lại code check token
- ✅ **Centralized**: Logic authentication ở một chỗ
- ✅ **Automatic**: Tự động áp dụng cho tất cả routes
- ✅ **Flexible**: Dễ dàng exclude public routes

### Bài tập về nhà:

1. Thay đổi access token expire time thành 10 giây để test nhanh
2. Tạo thêm protected APIs
3. Test refresh token flow

### Bài tiếp theo:

**Bài 5**: Logout, Token Blacklist và Advanced Features

---

**🎉 Tuyệt vời! Bạn đã hiểu Middleware và Refresh Token!**

*Middleware giúp code sạch hơn và Refresh Token cải thiện UX rất nhiều!*
