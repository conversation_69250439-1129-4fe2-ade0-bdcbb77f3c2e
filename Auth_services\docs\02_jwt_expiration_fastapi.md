# 🕐 JWT Bước 2: Thêm Thời Gian Hết Hạn và FastAPI Đơn Giản

## 📚 Mục tiêu của bài này
- Thêm thời gian hết hạn cho JWT token
- Tạo API đăng nhập đơn giản với FastAPI
- Tạo API được bảo vệ bởi JWT
- Hiểu cách JWT hoạt động trong web

---

## 🕐 Bước 1: Hiểu Thời Gian Hết Hạn

### Tại sao cần thời gian hết hạn?

**Ví dụ thực tế:**
- **Vé xem phim**: Chỉ dùng được trong ngày hôm đó
- **Vé xe bus**: Chỉ dùng được trong 2 giờ
- **JWT token**: Chỉ dùng được trong 30 phút

**Lý do:**
- **<PERSON><PERSON><PERSON> mật**: Nếu token bị đánh cắp, chỉ sử dụng được trong thời gian ngắn
- **Kiểm soát**: Server có thể "thu hồi" quyền truy cập

### Thời gian hết hạn trong JWT

```python
# Thông tin trong token sẽ có thêm:
{
    "user_id": 123,
    "username": "john_doe",
    "exp": 1640995200  # Thời gian hết hạn (timestamp)
}
```

**Giải thích:**
- `exp`: Expiration time (thời gian hết hạn)
- Là timestamp (số giây từ 1/1/1970)
- JWT tự động kiểm tra thời gian này

---

## 🔧 Bước 2: Tạo JWT với Thời Gian Hết Hạn

### 2.1 Tạo file `jwt_with_expiration.py`

```python
# jwt_with_expiration.py
from jose import jwt
from datetime import datetime, timedelta

print("🕐 JWT với thời gian hết hạn!\n")

# Cấu hình
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"
```

### 2.2 Hàm tạo token với thời gian hết hạn

```python
def create_token_with_expiration(user_info, expire_minutes=30):
    """
    Tạo JWT token với thời gian hết hạn
    
    Args:
        user_info: Thông tin user
        expire_minutes: Số phút token sẽ hết hạn
    
    Returns:
        str: JWT token
    """
    # Tính thời gian hết hạn
    expire_time = datetime.utcnow() + timedelta(minutes=expire_minutes)
    
    print(f"⏰ Thời gian hiện tại: {datetime.utcnow()}")
    print(f"⏰ Token sẽ hết hạn lúc: {expire_time}")
    print(f"⏰ Token sống trong: {expire_minutes} phút")
```

**Giải thích từng dòng:**
- `datetime.utcnow()`: Lấy thời gian hiện tại (UTC)
- `timedelta(minutes=30)`: Thêm 30 phút
- `expire_time`: Thời gian token sẽ hết hạn

```python
    # Thêm thời gian hết hạn vào thông tin user
    token_data = user_info.copy()  # Copy để không thay đổi dữ liệu gốc
    token_data["exp"] = expire_time  # Thêm thời gian hết hạn
    
    print(f"📋 Dữ liệu trong token: {token_data}")
    
    # Tạo token
    token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    print(f"🎫 Token được tạo: {token[:50]}...")
    return token
```

**Giải thích:**
- `user_info.copy()`: Tạo bản copy để không thay đổi dữ liệu gốc
- `token_data["exp"] = expire_time`: Thêm thời gian hết hạn
- JWT sẽ tự động kiểm tra field `exp` này

### 2.3 Hàm kiểm tra token

```python
def verify_token_with_expiration(token):
    """
    Kiểm tra token có hợp lệ và chưa hết hạn không
    
    Args:
        token: JWT token string
    
    Returns:
        dict hoặc None: Thông tin user nếu token hợp lệ, None nếu không
    """
    try:
        # Giải mã token
        decoded_data = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        print("✅ Token hợp lệ và chưa hết hạn!")
        print(f"📋 Thông tin user: {decoded_data}")
        
        return decoded_data
        
    except jwt.ExpiredSignatureError:
        print("❌ Token đã hết hạn!")
        return None
        
    except jwt.JWTError as error:
        print(f"❌ Token không hợp lệ: {error}")
        return None
```

**Giải thích:**
- `jwt.ExpiredSignatureError`: Lỗi đặc biệt khi token hết hạn
- `jwt.JWTError`: Lỗi chung cho tất cả vấn đề JWT khác
- JWT tự động kiểm tra thời gian hết hạn

### 2.4 Test thử

```python
# Test tạo token
user_info = {
    "user_id": 123,
    "username": "john_doe",
    "role": "user"
}

print("=" * 50)
print("🧪 TEST 1: Tạo token với thời gian hết hạn 1 phút")
token = create_token_with_expiration(user_info, expire_minutes=1)

print("\n" + "=" * 50)
print("🧪 TEST 2: Kiểm tra token ngay lập tức")
result = verify_token_with_expiration(token)

print("\n" + "=" * 50)
print("🧪 TEST 3: Đợi 65 giây rồi kiểm tra lại...")
print("(Trong thực tế, bạn có thể chạy lại script sau 1 phút)")

# Để test thực sự, uncomment dòng dưới:
# import time
# time.sleep(65)  # Đợi 65 giây
# result = verify_token_with_expiration(token)
```

---

## 🌐 Bước 3: Tạo FastAPI Đơn Giản

### 3.1 Cài đặt FastAPI

```bash
pip install fastapi uvicorn
```

### 3.2 Tạo file `simple_jwt_api.py`

```python
# simple_jwt_api.py
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from jose import jwt
from datetime import datetime, timedelta

# Tạo FastAPI app
app = FastAPI(title="JWT API Đơn Giản")

# Cấu hình JWT
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

print("🚀 FastAPI JWT API đã khởi động!")
```

### 3.3 Tạo models cho request/response

```python
# Models để định nghĩa dữ liệu
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    message: str
    token: str
    expires_in: int  # Số giây token sẽ hết hạn

# Fake database user (thực tế sẽ dùng database thật)
FAKE_USERS = {
    "admin": "password123",
    "user1": "mypassword"
}

print("👥 Fake users:", list(FAKE_USERS.keys()))
```

**Giải thích:**
- `BaseModel`: Pydantic model để validate dữ liệu
- `FAKE_USERS`: Database giả để demo
- Thực tế sẽ dùng database thật và hash password

### 3.4 API đăng nhập

```python
@app.post("/login", response_model=LoginResponse)
def login(login_data: LoginRequest):
    """
    API đăng nhập - trả về JWT token
    
    Args:
        login_data: Username và password
    
    Returns:
        LoginResponse: Message và JWT token
    """
    username = login_data.username
    password = login_data.password
    
    print(f"🔐 Đăng nhập: {username}")
    
    # Kiểm tra user có tồn tại không
    if username not in FAKE_USERS:
        print(f"❌ User {username} không tồn tại")
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    # Kiểm tra password
    if FAKE_USERS[username] != password:
        print(f"❌ Sai password cho user {username}")
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    print(f"✅ Đăng nhập thành công: {username}")
```

**Giải thích từng bước:**
- `@app.post("/login")`: Tạo POST endpoint tại `/login`
- `response_model=LoginResponse`: FastAPI tự động validate response
- `HTTPException(status_code=401)`: Trả về lỗi 401 Unauthorized
- Kiểm tra username và password từ fake database

```python
    # Tạo JWT token
    expire_minutes = 30
    expire_time = datetime.utcnow() + timedelta(minutes=expire_minutes)
    
    token_data = {
        "user_id": hash(username) % 1000,  # Fake user ID
        "username": username,
        "exp": expire_time
    }
    
    token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    print(f"🎫 Token được tạo cho {username}")
    
    return LoginResponse(
        message=f"Đăng nhập thành công! Chào {username}",
        token=token,
        expires_in=expire_minutes * 60  # Convert phút thành giây
    )
```

**Giải thích:**
- Tạo token với thời gian hết hạn 30 phút
- `hash(username) % 1000`: Tạo fake user ID từ username
- Return response theo format đã định nghĩa

### 3.5 API được bảo vệ

```python
@app.get("/protected")
def protected_route():
    """
    API được bảo vệ - cần JWT token để truy cập
    
    Tạm thời chưa check token, sẽ làm ở bước tiếp theo
    """
    return {
        "message": "🔒 Đây là dữ liệu được bảo vệ!",
        "data": "Chỉ user đã đăng nhập mới thấy được"
    }

@app.get("/")
def root():
    """
    API công khai - không cần token
    """
    return {
        "message": "🌟 Chào mừng đến với JWT API!",
        "endpoints": {
            "login": "POST /login - Đăng nhập để lấy token",
            "protected": "GET /protected - Cần token để truy cập"
        }
    }
```

---

## 🧪 Bước 4: Test API

### 4.1 Chạy server

```bash
uvicorn simple_jwt_api:app --reload --port 8000
```

**Giải thích:**
- `simple_jwt_api:app`: File `simple_jwt_api.py`, biến `app`
- `--reload`: Tự động restart khi code thay đổi
- `--port 8000`: Chạy trên port 8000

### 4.2 Test với curl

**1. Test API công khai:**
```bash
curl http://localhost:8000/
```

**Kết quả mong đợi:**
```json
{
  "message": "🌟 Chào mừng đến với JWT API!",
  "endpoints": {
    "login": "POST /login - Đăng nhập để lấy token",
    "protected": "GET /protected - Cần token để truy cập"
  }
}
```

**2. Test đăng nhập:**
```bash
curl -X POST "http://localhost:8000/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'
```

**Kết quả mong đợi:**
```json
{
  "message": "Đăng nhập thành công! Chào admin",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 1800
}
```

**3. Test với sai password:**
```bash
curl -X POST "http://localhost:8000/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "wrongpassword"}'
```

**Kết quả mong đợi:**
```json
{
  "detail": "Sai username hoặc password"
}
```

### 4.3 Test với Swagger UI

Mở trình duyệt và vào: `http://localhost:8000/docs`

**Bạn sẽ thấy:**
- Giao diện Swagger UI tự động
- Có thể test API trực tiếp trên web
- Xem cấu trúc request/response

---

## 📝 Bước 5: Code Hoàn Chỉnh

### File `simple_jwt_api.py` hoàn chỉnh:

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from jose import jwt
from datetime import datetime, timedelta

# FastAPI app
app = FastAPI(title="JWT API Đơn Giản", version="1.0.0")

# JWT config
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

# Models
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    message: str
    token: str
    expires_in: int

# Fake database
FAKE_USERS = {
    "admin": "password123",
    "user1": "mypassword"
}

@app.get("/")
def root():
    return {
        "message": "🌟 Chào mừng đến với JWT API!",
        "users": list(FAKE_USERS.keys()),
        "endpoints": {
            "login": "POST /login",
            "protected": "GET /protected"
        }
    }

@app.post("/login", response_model=LoginResponse)
def login(login_data: LoginRequest):
    username = login_data.username
    password = login_data.password
    
    # Validate user
    if username not in FAKE_USERS or FAKE_USERS[username] != password:
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    # Create token
    expire_minutes = 30
    expire_time = datetime.utcnow() + timedelta(minutes=expire_minutes)
    
    token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "exp": expire_time
    }
    
    token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    return LoginResponse(
        message=f"Đăng nhập thành công! Chào {username}",
        token=token,
        expires_in=expire_minutes * 60
    )

@app.get("/protected")
def protected_route():
    return {
        "message": "🔒 Dữ liệu được bảo vệ!",
        "note": "Bài tiếp theo sẽ học cách check JWT token ở đây"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

---

## 🎯 Tóm tắt Bài 2

### Những gì đã học:

1. **Thời gian hết hạn**: Thêm `exp` vào JWT token
2. **FastAPI cơ bản**: Tạo API đăng nhập đơn giản
3. **Pydantic Models**: Validate request/response data
4. **Error Handling**: Xử lý lỗi với HTTPException

### Những gì chưa làm (sẽ làm ở bài sau):

- ❌ Kiểm tra JWT token trong API protected
- ❌ Lấy thông tin user từ token
- ❌ Middleware tự động check token

### Bài tập về nhà:

1. Thêm user mới vào `FAKE_USERS`
2. Thay đổi thời gian hết hạn token
3. Test với Swagger UI

### Bài tiếp theo:

**Bài 3**: Kiểm tra JWT token trong API và tạo middleware đơn giản

---

**🎉 Tuyệt vời! Bạn đã tạo được API đăng nhập với JWT!**

*Hãy chạy server và test trước khi chuyển sang bài tiếp theo nhé!*
