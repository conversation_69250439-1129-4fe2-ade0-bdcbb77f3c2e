# 🎯 JWT Bước 1: Hiểu JWT và Tạo Token Đầu Tiên

## 📚 Mục tiêu của bài này
- Hiểu JWT là gì một cách đơn giản nhất
- Tạo JWT token đầu tiên
- Ki<PERSON><PERSON> tra token có hoạt động không
- Chưa làm gì phức tạp, chỉ tập trung hiểu JWT

---

## 🤔 JWT là gì? (Gi<PERSON>i thích đơn giản)

### Ví dụ thực tế:
Hãy tưởng tượng JWT như **vé xem phim**:

1. **Bạn mua vé** = Đăng nhập
2. **Vé có thông tin**: Tên phim, ghế, thời gian
3. **Vào rạp**: Nhân viên kiểm tra vé → Cho vào
4. **V<PERSON> hết hạn**: <PERSON>u giờ chiếu, vé không dùng được nữa

**JWT cũng vậy:**
1. **User đăng nhập** = Tạo JWT token
2. **Token chứa thông tin**: User ID, tên, quyền hạn
3. **Gửi request**: Server kiểm tra token → Cho phép truy cập
4. **Token hết hạn**: Sau 30 phút, token không dùng được

### JWT trông như thế nào?

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**Có 3 phần, cách nhau bởi dấu chấm:**
- **Phần 1**: Thông tin về thuật toán mã hóa
- **Phần 2**: Dữ liệu user (tên, ID, quyền)
- **Phần 3**: Chữ ký để đảm bảo token không bị giả mạo

---

## 🛠️ Bước 1: Cài đặt thư viện (Rất đơn giản)

### 1.1 Cài đặt
```bash
pip install python-jose[cryptography]
```

**Giải thích:**
- `python-jose`: Thư viện để tạo và kiểm tra JWT
- `[cryptography]`: Thêm tính năng mã hóa mạnh

### 1.2 Tạo file đầu tiên
Tạo file `test_jwt_simple.py` để thử nghiệm:

```python
# test_jwt_simple.py
from jose import jwt
from datetime import datetime, timedelta

print("🎯 Bắt đầu học JWT!")
```

---

## 🔧 Bước 2: Tạo JWT Token Đầu Tiên

### 2.1 Code đơn giản nhất

```python
# test_jwt_simple.py
from jose import jwt
from datetime import datetime, timedelta

# Bước 1: Chuẩn bị thông tin cần thiết
SECRET_KEY = "my-secret-key-123"  # Khóa bí mật (như mật khẩu)
ALGORITHM = "HS256"               # Thuật toán mã hóa

print("🔑 Secret key:", SECRET_KEY)
print("🔒 Algorithm:", ALGORITHM)
```

**Giải thích từng dòng:**
- `SECRET_KEY`: Như mật khẩu để tạo và kiểm tra token
- `ALGORITHM`: Cách thức mã hóa (HS256 là phổ biến nhất)

### 2.2 Tạo thông tin user

```python
# Bước 2: Thông tin user muốn lưu trong token
user_info = {
    "user_id": 123,           # ID của user
    "username": "john_doe",   # Tên đăng nhập
    "role": "user"           # Vai trò (user, admin, etc.)
}

print("👤 User info:", user_info)
```

**Giải thích:**
- Đây là thông tin sẽ được "nhét" vào token
- Khi kiểm tra token, ta sẽ lấy lại được thông tin này

### 2.3 Tạo token

```python
# Bước 3: Tạo JWT token
token = jwt.encode(user_info, SECRET_KEY, algorithm=ALGORITHM)

print("🎫 Token được tạo:")
print(token)
print()
print("📏 Độ dài token:", len(token), "ký tự")
```

**Giải thích:**
- `jwt.encode()`: Hàm tạo token từ thông tin user
- Cần 3 thứ: thông tin user, secret key, algorithm
- Kết quả là một chuỗi dài (token)

### 2.4 Chạy thử

```bash
python test_jwt_simple.py
```

**Kết quả sẽ như:**
```
🔑 Secret key: my-secret-key-123
🔒 Algorithm: HS256
👤 User info: {'user_id': 123, 'username': 'john_doe', 'role': 'user'}
🎫 Token được tạo:
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMjMsInVzZXJuYW1lIjoiam9obl9kb2UiLCJyb2xlIjoidXNlciJ9.abc123xyz...
📏 Độ dài token: 150 ký tự
```

---

## 🔍 Bước 3: Kiểm tra Token (Decode)

### 3.1 Giải mã token

```python
# Bước 4: Kiểm tra token có đúng không
try:
    # Giải mã token để lấy lại thông tin
    decoded_info = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    
    print("✅ Token hợp lệ!")
    print("📋 Thông tin trong token:", decoded_info)
    
except Exception as error:
    print("❌ Token không hợp lệ:", error)
```

**Giải thích từng dòng:**
- `jwt.decode()`: Hàm giải mã token để lấy lại thông tin
- Cần cùng secret key và algorithm như lúc tạo
- `try/except`: Bắt lỗi nếu token không hợp lệ

### 3.2 So sánh thông tin

```python
# Bước 5: So sánh thông tin gốc và thông tin từ token
print("\n🔍 So sánh:")
print("Thông tin gốc    :", user_info)
print("Thông tin từ token:", decoded_info)

# Kiểm tra từng thông tin
if user_info["user_id"] == decoded_info["user_id"]:
    print("✅ User ID khớp")
else:
    print("❌ User ID không khớp")

if user_info["username"] == decoded_info["username"]:
    print("✅ Username khớp")
else:
    print("❌ Username không khớp")
```

**Giải thích:**
- So sánh thông tin gốc với thông tin giải mã từ token
- Nếu khớp → Token hoạt động đúng

---

## 🧪 Bước 4: Thử nghiệm với Token Sai

### 4.1 Test với secret key sai

```python
# Bước 6: Thử với secret key sai
print("\n🧪 Test với secret key sai:")
wrong_secret = "wrong-secret-key"

try:
    wrong_decode = jwt.decode(token, wrong_secret, algorithms=[ALGORITHM])
    print("❌ Lỗi: Token vẫn decode được với secret key sai!")
    
except Exception as error:
    print("✅ Đúng rồi: Token không decode được với secret key sai")
    print("📝 Lỗi:", str(error))
```

**Giải thích:**
- Thử decode token với secret key khác
- Phải báo lỗi → Chứng tỏ token bảo mật

### 4.2 Test với token bị sửa đổi

```python
# Bước 7: Thử với token bị sửa đổi
print("\n🧪 Test với token bị sửa đổi:")
fake_token = token[:-5] + "FAKE!"  # Thay 5 ký tự cuối bằng "FAKE!"

try:
    fake_decode = jwt.decode(fake_token, SECRET_KEY, algorithms=[ALGORITHM])
    print("❌ Lỗi: Token giả vẫn decode được!")
    
except Exception as error:
    print("✅ Đúng rồi: Token giả không decode được")
    print("📝 Lỗi:", str(error))
```

**Giải thích:**
- Sửa đổi token một chút
- Phải báo lỗi → Chứng tỏ token không thể giả mạo

---

## 📝 Bước 5: Code Hoàn Chỉnh

### File `test_jwt_simple.py` hoàn chỉnh:

```python
from jose import jwt
from datetime import datetime, timedelta

print("🎯 Bắt đầu học JWT!\n")

# Bước 1: Chuẩn bị
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

print("🔑 Secret key:", SECRET_KEY)
print("🔒 Algorithm:", ALGORITHM)

# Bước 2: Thông tin user
user_info = {
    "user_id": 123,
    "username": "john_doe", 
    "role": "user"
}

print("👤 User info:", user_info)

# Bước 3: Tạo token
token = jwt.encode(user_info, SECRET_KEY, algorithm=ALGORITHM)

print("\n🎫 Token được tạo:")
print(token)
print("📏 Độ dài:", len(token), "ký tự")

# Bước 4: Kiểm tra token
try:
    decoded_info = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    print("\n✅ Token hợp lệ!")
    print("📋 Thông tin trong token:", decoded_info)
    
except Exception as error:
    print("\n❌ Token không hợp lệ:", error)

# Bước 5: So sánh
print("\n🔍 So sánh:")
print("Gốc  :", user_info)
print("Token:", decoded_info)

# Bước 6: Test secret key sai
print("\n🧪 Test secret key sai:")
try:
    jwt.decode(token, "wrong-key", algorithms=[ALGORITHM])
    print("❌ Lỗi: Vẫn decode được!")
except:
    print("✅ Đúng: Không decode được với secret key sai")

# Bước 7: Test token giả
print("\n🧪 Test token giả:")
fake_token = token[:-5] + "FAKE!"
try:
    jwt.decode(fake_token, SECRET_KEY, algorithms=[ALGORITHM])
    print("❌ Lỗi: Token giả vẫn decode được!")
except:
    print("✅ Đúng: Token giả không decode được")

print("\n🎉 Hoàn thành! Bạn đã hiểu JWT cơ bản!")
```

---

## 🎯 Tóm tắt Bài 1

### Những gì đã học:

1. **JWT là gì**: Như vé xem phim, chứa thông tin user
2. **Tạo token**: `jwt.encode(thông_tin, secret_key, algorithm)`
3. **Kiểm tra token**: `jwt.decode(token, secret_key, algorithms)`
4. **Bảo mật**: Token không thể giả mạo nếu không có secret key

### Những gì chưa học (sẽ học ở bài sau):

- ❌ Thời gian hết hạn
- ❌ Sử dụng trong web API
- ❌ Login/logout
- ❌ Bảo vệ routes

### Bài tập về nhà:

1. Thay đổi `user_info` với thông tin khác
2. Thử với `SECRET_KEY` khác
3. Chạy code và quan sát kết quả

### Bài tiếp theo:

**Bài 2**: Thêm thời gian hết hạn và sử dụng JWT trong FastAPI đơn giản

---

**🎉 Chúc mừng! Bạn đã hiểu JWT cơ bản rồi!**

*Hãy chạy code và thử nghiệm trước khi chuyển sang bài tiếp theo nhé!*
