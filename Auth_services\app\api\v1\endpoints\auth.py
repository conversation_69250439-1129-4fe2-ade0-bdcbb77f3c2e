from fastapi import APIRouter, Depends 
from ....controllers.auth_controllers import AuthController
from ....schemas.auth import RegisterRequest, UserResponse, TokenResponse, LoginRequest

router = APIRouter()

@router.post("/register", response_model= UserResponse)
async def register(
    payload: RegisterRequest, 
    controller: AuthController = Depends()
): 
    return controller.register(payload= payload)

@router.post("/login", response_model= TokenResponse)
async def login(
    payload: LoginRequest, 
    controller: AuthController = Depends()
):
    return controller.login(payload)
