from fastapi.responses import JSONResponse
from fastapi import Request, status 
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from typing import Callable, Awaitable

from starlette.responses import Response
from ..core.security.jwt import jwt_handler

class AuthenticationMiddleWare(BaseHTTPMiddleware): 
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        
        # bỏ qua path không cần kiểm tra token 
        if request.url.path.startswith("auth/login"): 
            return call_next(request)
        
         # Lấy token từ header
        auth_header = request.headers.get("Authorization")
        # beares <token>
        if not auth_header:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Missing Authorization header"}
            )
        
        try: 
            schemas, token = auth_header.split(" ")
            if schemas != "bearer":
                raise ValueError
        except Exception as e: 
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid token format"}
            )
        
         # Verify token
        payload = jwt_handler.decode_access_token(token=token)
        if not payload: 
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid or expired token"}
            )

        # Gắn user info vào request.state để route dùng
        request.state.current_user = payload
        return await call_next(request)


        

        