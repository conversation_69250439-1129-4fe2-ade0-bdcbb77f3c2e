# 🛡️ JWT Bước 3: <PERSON><PERSON><PERSON> Vệ Routes Đơn Giản

## 📚 M<PERSON>c tiêu của bài này
- Học cách kiểm tra JWT token trong API
- Bảo vệ routes với JWT token
- Lấy thông tin user từ token
- <PERSON><PERSON><PERSON> cách hoạt động của Authorization header

---

## 🔍 Bước 1: Hiểu Authorization Header

### Authorization Header là gì?

**Ví dụ thực tế:**
- **Vào công ty**: Đưa thẻ nhân viên cho bảo vệ
- **Vào ngân hàng**: Đưa CMND cho nhân viên
- **API request**: Đưa JWT token trong header

### Format của Authorization Header:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Gi<PERSON>i thích:**
- `Authorization`: Tên header
- `Bearer`: <PERSON>ại token (c<PERSON> <PERSON>h<PERSON><PERSON> là "người mang token")
- `eyJhbGci...`: JWT token thực tế

### Cách client gửi token:

```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" http://localhost:8000/protected
```

---

## 🔧 Bước 2: Tạo Hàm Kiểm Tra Token

### 2.1 Tạo file `jwt_checker.py`

```python
# jwt_checker.py
from jose import jwt, JWTError
from fastapi import HTTPException, status

# Cấu hình (giống như bài trước)
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

def verify_jwt_token(token: str):
    """
    Kiểm tra JWT token có hợp lệ không
    
    Args:
        token: JWT token string (không có "Bearer ")
    
    Returns:
        dict: Thông tin user nếu token hợp lệ
    
    Raises:
        HTTPException: Nếu token không hợp lệ
    """
    try:
        # Giải mã token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        print(f"✅ Token hợp lệ cho user: {payload.get('username')}")
        return payload
        
    except jwt.ExpiredSignatureError:
        print("❌ Token đã hết hạn")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token đã hết hạn, vui lòng đăng nhập lại"
        )
        
    except JWTError:
        print("❌ Token không hợp lệ")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token không hợp lệ"
        )
```

**Giải thích từng phần:**
- `jwt.ExpiredSignatureError`: Lỗi khi token hết hạn
- `JWTError`: Lỗi chung cho token không hợp lệ
- `HTTPException`: FastAPI exception để trả về lỗi HTTP
- `status.HTTP_401_UNAUTHORIZED`: Status code 401

### 2.2 Test hàm kiểm tra

```python
# Test function
if __name__ == "__main__":
    # Tạo token test
    from datetime import datetime, timedelta
    
    test_token_data = {
        "user_id": 123,
        "username": "test_user",
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    
    test_token = jwt.encode(test_token_data, SECRET_KEY, algorithm=ALGORITHM)
    print(f"🧪 Test token: {test_token[:50]}...")
    
    # Test verify
    try:
        result = verify_jwt_token(test_token)
        print(f"✅ Test thành công: {result}")
    except HTTPException as e:
        print(f"❌ Test thất bại: {e.detail}")
```

---

## 🛡️ Bước 3: Bảo Vệ API Routes

### 3.1 Cập nhật file `simple_jwt_api.py`

```python
# Thêm vào đầu file
from fastapi import FastAPI, HTTPException, Header
from typing import Optional

# Import hàm verify từ file vừa tạo
from jwt_checker import verify_jwt_token
```

### 3.2 Tạo hàm lấy token từ header

```python
def get_token_from_header(authorization: Optional[str] = Header(None)):
    """
    Lấy JWT token từ Authorization header
    
    Args:
        authorization: Authorization header từ request
    
    Returns:
        str: JWT token (đã bỏ "Bearer ")
    
    Raises:
        HTTPException: Nếu header không hợp lệ
    """
    if not authorization:
        print("❌ Không có Authorization header")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Thiếu Authorization header"
        )
    
    # Kiểm tra format: "Bearer <token>"
    try:
        scheme, token = authorization.split(" ")
        if scheme.lower() != "bearer":
            raise ValueError("Scheme không phải Bearer")
        
        print(f"✅ Tìm thấy Bearer token: {token[:20]}...")
        return token
        
    except ValueError:
        print(f"❌ Authorization header sai format: {authorization}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header phải có format: Bearer <token>"
        )
```

**Giải thích từng bước:**
- `Header(None)`: FastAPI tự động lấy header từ request
- `authorization.split(" ")`: Tách "Bearer" và token
- `scheme.lower() != "bearer"`: Kiểm tra phải là "Bearer"
- Return token đã bỏ phần "Bearer "

### 3.3 Cập nhật API protected

```python
@app.get("/protected")
def protected_route(authorization: Optional[str] = Header(None)):
    """
    API được bảo vệ - cần JWT token hợp lệ
    
    Args:
        authorization: Authorization header
    
    Returns:
        dict: Dữ liệu được bảo vệ và thông tin user
    """
    print("🔒 Truy cập API được bảo vệ")
    
    # Bước 1: Lấy token từ header
    token = get_token_from_header(authorization)
    
    # Bước 2: Verify token
    user_info = verify_jwt_token(token)
    
    # Bước 3: Trả về dữ liệu được bảo vệ
    return {
        "message": "🎉 Truy cập thành công!",
        "user_info": {
            "user_id": user_info.get("user_id"),
            "username": user_info.get("username")
        },
        "protected_data": [
            "Dữ liệu bí mật số 1",
            "Thông tin quan trọng",
            "Chỉ user đã đăng nhập mới thấy được"
        ],
        "server_time": datetime.utcnow().isoformat()
    }
```

**Giải thích flow:**
1. Client gửi request với Authorization header
2. `get_token_from_header()`: Lấy token từ header
3. `verify_jwt_token()`: Kiểm tra token hợp lệ
4. Nếu OK → Trả về dữ liệu được bảo vệ
5. Nếu lỗi → Tự động trả về 401 Unauthorized

### 3.4 Thêm API lấy thông tin user

```python
@app.get("/me")
def get_my_info(authorization: Optional[str] = Header(None)):
    """
    API lấy thông tin user hiện tại
    
    Args:
        authorization: Authorization header
    
    Returns:
        dict: Thông tin chi tiết của user
    """
    print("👤 Lấy thông tin user hiện tại")
    
    # Lấy và verify token
    token = get_token_from_header(authorization)
    user_info = verify_jwt_token(token)
    
    return {
        "message": "Thông tin user hiện tại",
        "user_details": {
            "user_id": user_info.get("user_id"),
            "username": user_info.get("username"),
            "token_expires_at": user_info.get("exp"),
            "login_time": "Khi token được tạo"
        },
        "permissions": [
            "read_own_data",
            "update_profile",
            "access_protected_routes"
        ]
    }
```

---

## 🧪 Bước 4: Test API Được Bảo Vệ

### 4.1 Chạy server

```bash
uvicorn simple_jwt_api:app --reload --port 8000
```

### 4.2 Test flow hoàn chỉnh

**Bước 1: Đăng nhập để lấy token**
```bash
curl -X POST "http://localhost:8000/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'
```

**Kết quả:**
```json
{
  "message": "Đăng nhập thành công! Chào admin",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2NzMsInVzZXJuYW1lIjoiYWRtaW4iLCJleHAiOjE2NDA5OTUyMDB9.abc123...",
  "expires_in": 1800
}
```

**Bước 2: Copy token và test API protected**
```bash
# Thay YOUR_TOKEN bằng token từ bước 1
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/protected
```

**Kết quả thành công:**
```json
{
  "message": "🎉 Truy cập thành công!",
  "user_info": {
    "user_id": 673,
    "username": "admin"
  },
  "protected_data": [
    "Dữ liệu bí mật số 1",
    "Thông tin quan trọng",
    "Chỉ user đã đăng nhập mới thấy được"
  ]
}
```

**Bước 3: Test API /me**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/me
```

### 4.3 Test các trường hợp lỗi

**1. Không có Authorization header:**
```bash
curl http://localhost:8000/protected
```
**Kết quả:** 401 - "Thiếu Authorization header"

**2. Sai format header:**
```bash
curl -H "Authorization: InvalidToken" \
  http://localhost:8000/protected
```
**Kết quả:** 401 - "Authorization header phải có format: Bearer <token>"

**3. Token không hợp lệ:**
```bash
curl -H "Authorization: Bearer invalid-token-123" \
  http://localhost:8000/protected
```
**Kết quả:** 401 - "Token không hợp lệ"

**4. Token hết hạn:**
```bash
# Tạo token với thời gian hết hạn 1 giây, đợi 2 giây rồi test
```

---

## 📝 Bước 5: Code Hoàn Chỉnh

### File `simple_jwt_api.py` hoàn chỉnh:

```python
from fastapi import FastAPI, HTTPException, Header, status
from pydantic import BaseModel
from jose import jwt, JWTError
from datetime import datetime, timedelta
from typing import Optional

# FastAPI app
app = FastAPI(title="JWT API với Protected Routes", version="1.0.0")

# JWT config
SECRET_KEY = "my-secret-key-123"
ALGORITHM = "HS256"

# Models
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    message: str
    token: str
    expires_in: int

# Fake database
FAKE_USERS = {
    "admin": "password123",
    "user1": "mypassword"
}

def verify_jwt_token(token: str):
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token đã hết hạn"
        )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token không hợp lệ"
        )

def get_token_from_header(authorization: Optional[str] = Header(None)):
    """Get token from Authorization header"""
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Thiếu Authorization header"
        )
    
    try:
        scheme, token = authorization.split(" ")
        if scheme.lower() != "bearer":
            raise ValueError("Invalid scheme")
        return token
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header phải có format: Bearer <token>"
        )

@app.get("/")
def root():
    return {
        "message": "🌟 JWT API với Protected Routes!",
        "endpoints": {
            "login": "POST /login - Đăng nhập",
            "protected": "GET /protected - Cần token",
            "me": "GET /me - Thông tin user"
        }
    }

@app.post("/login", response_model=LoginResponse)
def login(login_data: LoginRequest):
    username = login_data.username
    password = login_data.password
    
    if username not in FAKE_USERS or FAKE_USERS[username] != password:
        raise HTTPException(status_code=401, detail="Sai username hoặc password")
    
    expire_minutes = 30
    expire_time = datetime.utcnow() + timedelta(minutes=expire_minutes)
    
    token_data = {
        "user_id": hash(username) % 1000,
        "username": username,
        "exp": expire_time
    }
    
    token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)
    
    return LoginResponse(
        message=f"Đăng nhập thành công! Chào {username}",
        token=token,
        expires_in=expire_minutes * 60
    )

@app.get("/protected")
def protected_route(authorization: Optional[str] = Header(None)):
    token = get_token_from_header(authorization)
    user_info = verify_jwt_token(token)
    
    return {
        "message": "🎉 Truy cập thành công!",
        "user_info": {
            "user_id": user_info.get("user_id"),
            "username": user_info.get("username")
        },
        "protected_data": ["Dữ liệu bí mật", "Thông tin quan trọng"]
    }

@app.get("/me")
def get_my_info(authorization: Optional[str] = Header(None)):
    token = get_token_from_header(authorization)
    user_info = verify_jwt_token(token)
    
    return {
        "message": "Thông tin user hiện tại",
        "user_details": {
            "user_id": user_info.get("user_id"),
            "username": user_info.get("username")
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

---

## 🎯 Tóm tắt Bài 3

### Những gì đã học:

1. **Authorization Header**: Format "Bearer <token>"
2. **Token Verification**: Kiểm tra token hợp lệ và chưa hết hạn
3. **Protected Routes**: API cần token để truy cập
4. **Error Handling**: Xử lý các trường hợp lỗi token

### Những gì chưa làm (sẽ làm ở bài sau):

- ❌ Middleware tự động check token cho tất cả routes
- ❌ Refresh token để tránh đăng nhập lại
- ❌ Role-based authorization (admin, user)

### Bài tập về nhà:

1. Tạo thêm API protected khác
2. Test với token hết hạn
3. Thử với Swagger UI

### Bài tiếp theo:

**Bài 4**: Middleware tự động check token và Refresh Token đơn giản

---

**🎉 Tuyệt vời! Bạn đã biết cách bảo vệ API với JWT!**

*Hãy test kỹ các trường hợp trước khi chuyển sang bài tiếp theo nhé!*
