# Cấu Trúc Dự Án Microservices - Hệ Thống Phân Tích Vết Thương

## Tổng Quan Dự Án

Dự án hiện tại đang được xây dựng theo kiến trúc **Microservices** với FastAPI, tập trung vào việc phát triển hệ thống AI phân tích và nhận diện vết thương da. Hiện tại đã có **Auth Service** hoàn chỉnh.

## Cấu Trúc Thư <PERSON>h

```
MicroService/
├── Auth_services/          # Service xác thực và phân quyền
├── docs/                   # Tài liệu dự án
└── [Future Services]/      # Các service khác sẽ được thêm
```

## Chi Tiết Auth Service

### Cấu Trúc Tổng Thể
```
Auth_services/
├── app/                    # Mã nguồn chính của service
├── docs/                   # Tài liệu hướng dẫn JWT và Authentication
├── jwt_basic_example.py    # Ví dụ cơ bản về JWT
├── requirements.txt        # Dependencies của service
└── run.py                 # Entry point để chạy service
```

### Kiến Trúc Bên Trong App (Clean Architecture)

```
app/
├── main.py                 # FastAPI application entry point
├── __init__.py            # Package initialization
│
├── api/                   # API Layer - Định nghĩa endpoints
│   └── v1/               # API version 1
│
├── controllers/           # Controller Layer - Xử lý business logic
│   ├── __init__.py
│   ├── auth_controllers.py    # Authentication controllers
│   └── base_controller.py     # Base controller class
│
├── services/              # Service Layer - Business logic core
│   ├── __init__.py
│   └── auth_services.py       # Authentication services
│
├── models/                # Data Models - Database entities
│   ├── __init__.py
│   ├── user.py               # User model
│   ├── role.py               # Role model
│   └── user_role.py          # User-Role relationship
│
├── schemas/               # Pydantic Schemas - Data validation
│   ├── __init__.py
│   └── auth.py               # Authentication schemas
│
├── core/                  # Core Configuration
│   ├── __init__.py
│   ├── config.py             # App configuration
│   ├── database.py           # Database connection
│   └── security/             # Security utilities
│
├── Middleware/            # Custom Middleware
│   └── auth_middleware.py    # Authentication middleware
│
└── utils/                 # Utility Functions
    ├── __init__.py
    └── validate_helper.py    # Validation helpers
```

## Kiến Trúc Layered Architecture

### 1. **API Layer** (`api/`)
- **Mục đích**: Định nghĩa các REST endpoints
- **Chức năng**: Nhận HTTP requests, gọi controllers, trả về responses
- **Ví dụ**: `/api/v1/auth/login`, `/api/v1/auth/register`

### 2. **Controller Layer** (`controllers/`)
- **Mục đích**: Xử lý HTTP requests và responses
- **Chức năng**: Validate input, gọi services, format output
- **Ví dụ**: `AuthController.login()`, `AuthController.register()`

### 3. **Service Layer** (`services/`)
- **Mục đích**: Chứa business logic chính
- **Chức năng**: Xử lý logic nghiệp vụ, gọi database, external APIs
- **Ví dụ**: `AuthService.authenticate_user()`, `AuthService.create_token()`

### 4. **Model Layer** (`models/`)
- **Mục đích**: Định nghĩa database entities
- **Chức năng**: SQLAlchemy models, database relationships
- **Ví dụ**: `User`, `Role`, `UserRole`

### 5. **Schema Layer** (`schemas/`)
- **Mục đích**: Data validation và serialization
- **Chức năng**: Pydantic models cho request/response validation
- **Ví dụ**: `LoginRequest`, `TokenResponse`, `UserCreate`

## Tài Liệu Hướng Dẫn Có Sẵn

### Auth Service Documentation
```
Auth_services/docs/
├── 01_jwt_basic_practice.md       # JWT cơ bản
├── 01_jwt_step_by_step.md         # JWT từng bước
├── 02_jwt_expiration_fastapi.md   # JWT expiration
├── 02_middleware_authorization.md # Middleware authorization
├── 03_protect_routes_simple.md    # Bảo vệ routes
├── 03_refresh_token_flow.md       # Refresh token flow
├── 04_logout_blacklist.md         # Logout và blacklist
├── 04_middleware_refresh_simple.md # Middleware refresh
├── 05_advanced_features.md        # Tính năng nâng cao
├── 05_logout_blacklist_simple.md  # Logout blacklist đơn giản
├── authentication_examples.md     # Ví dụ authentication
├── authentication_guide.md        # Hướng dẫn authentication
├── jwt_advanced_guide.md          # JWT nâng cao
├── jwt_basic_guide.md            # JWT cơ bản
├── middleware_advanced.md         # Middleware nâng cao
└── step_by_step_tutorial.md      # Tutorial từng bước
```

## Kế Hoạch Mở Rộng Microservices

Dự án được thiết kế để dễ dàng mở rộng với các services khác:

```
MicroService/
├── Auth_services/          # ✅ Đã hoàn thành
├── Image_Processing_Service/   # 🔄 Dự kiến: Xử lý ảnh vết thương
├── AI_Analysis_Service/        # 🔄 Dự kiến: AI phân tích vết thương
├── Recommendation_Service/     # 🔄 Dự kiến: Đưa ra khuyến nghị
├── Notification_Service/       # 🔄 Dự kiến: Thông báo
├── API_Gateway/               # 🔄 Dự kiến: Gateway tổng hợp
└── docs/                      # 📚 Tài liệu chung
```

## Ưu Điểm Của Cấu Trúc Hiện Tại

1. **Separation of Concerns**: Mỗi layer có trách nhiệm riêng biệt
2. **Scalability**: Dễ dàng thêm services mới
3. **Maintainability**: Code được tổ chức rõ ràng, dễ bảo trì
4. **Testability**: Mỗi layer có thể test độc lập
5. **Documentation**: Tài liệu chi tiết cho từng tính năng

## Công Nghệ Sử Dụng

- **Framework**: FastAPI
- **Database**: SQL Server, PostgreSQL (Hybrid approach)
- **ORM**: SQLAlchemy + Raw SQL
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Pydantic
- **Architecture**: Clean Architecture + Microservices

Cấu trúc này cung cấp nền tảng vững chắc để phát triển hệ thống phân tích vết thương với khả năng mở rộng cao và dễ bảo trì.
