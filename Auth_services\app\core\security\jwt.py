from datetime import datetime, timedelta, timezone
from jose import jwt, J<PERSON><PERSON>rror
from typing import Dict, Any, Optional
from ..config import settings

class JWTHandler: 
    def __init__(
        self, 
        secret_key: str = settings.JWT_SECRET_KEY, 
        algorithm: str = settings.JWT_ALGORITHM, 
        access_token_expire_minutes: int = settings.ACCESS_TOKEN_EXPIRE_MINUTES
    ): 
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes 

    def create_access_token(self, user_id: int, user_name: str) -> str:

        now = datetime.now(timezone.utc)
        expire = now + timedelta(minutes=self.access_token_expire_minutes)

        payload: Dict[str, Any] = {
            "sub": str(user_id), 
            "username": user_name, 
            "iat": int(now.timestamp()),
            "exp": int(expire.timestamp())
        }

        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def decode_access_token(self, token: str) -> Optional[Dict[str, Any]]: 
        try: 
            payload = jwt.decode(token= token, key= self.secret_key, algorithms= self.algorithm)

            exp = payload.get("exp")
            if exp: 
                exp_dt = datetime.fromtimestamp(exp, tz=timezone.utc)
                if exp_dt < datetime.now(timezone.utc): 
                    return None
            return payload
        except JWTError: 
            return None
        
jwt_handler = JWTHandler()